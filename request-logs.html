<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>请求日志查询 - Qwen-Solve API</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f5f5f5;
            color: #333;
            line-height: 1.6;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }

        .header h1 {
            color: #2c3e50;
            margin-bottom: 10px;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }

        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            text-align: center;
        }

        .stat-value {
            font-size: 2em;
            font-weight: bold;
            color: #3498db;
            margin-bottom: 5px;
        }

        .stat-label {
            color: #7f8c8d;
            font-size: 0.9em;
        }

        .filters {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }

        .filter-row {
            display: flex;
            gap: 15px;
            align-items: end;
            flex-wrap: wrap;
        }

        .filter-group {
            display: flex;
            flex-direction: column;
            min-width: 150px;
        }

        .filter-group label {
            margin-bottom: 5px;
            font-weight: 500;
            color: #555;
        }

        .filter-group input,
        .filter-group select {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }

        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: background-color 0.3s;
        }

        .btn-primary {
            background-color: #3498db;
            color: white;
        }

        .btn-primary:hover {
            background-color: #2980b9;
        }

        .btn-secondary {
            background-color: #95a5a6;
            color: white;
        }

        .btn-secondary:hover {
            background-color: #7f8c8d;
        }

        .btn-danger {
            background-color: #e74c3c;
            color: white;
        }

        .btn-danger:hover {
            background-color: #c0392b;
        }

        .table-container {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
        }

        .table th,
        .table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #eee;
        }

        .table th {
            background-color: #f8f9fa;
            font-weight: 600;
            color: #555;
        }

        .table tr:hover {
            background-color: #f8f9fa;
        }

        .status-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }

        .status-success {
            background-color: #d4edda;
            color: #155724;
        }

        .status-error {
            background-color: #f8d7da;
            color: #721c24;
        }

        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 10px;
            padding: 20px;
            background: white;
            margin-top: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .pagination button {
            padding: 8px 12px;
            border: 1px solid #ddd;
            background: white;
            cursor: pointer;
            border-radius: 4px;
        }

        .pagination button:hover:not(:disabled) {
            background-color: #f8f9fa;
        }

        .pagination button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .pagination .active {
            background-color: #3498db;
            color: white;
            border-color: #3498db;
        }

        .loading {
            text-align: center;
            padding: 40px;
            color: #7f8c8d;
        }

        .error {
            background-color: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
        }

        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }

        .modal-content {
            background-color: white;
            margin: 5% auto;
            padding: 20px;
            border-radius: 8px;
            width: 80%;
            max-width: 800px;
            max-height: 80vh;
            overflow-y: auto;
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid #eee;
        }

        .close {
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
            color: #aaa;
        }

        .close:hover {
            color: #000;
        }

        .detail-grid {
            display: grid;
            grid-template-columns: 1fr 2fr;
            gap: 10px;
            margin-bottom: 15px;
        }

        .detail-label {
            font-weight: 600;
            color: #555;
        }

        .detail-value {
            word-break: break-all;
        }

        @media (max-width: 768px) {
            .filter-row {
                flex-direction: column;
                align-items: stretch;
            }
            
            .filter-group {
                min-width: auto;
            }
            
            .table-container {
                overflow-x: auto;
            }
            
            .modal-content {
                width: 95%;
                margin: 2% auto;
            }
            
            .detail-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 页面标题 -->
        <div class="header">
            <h1>📊 请求日志查询</h1>
            <p>查看和管理 Qwen-Solve API 的请求日志</p>
        </div>

        <!-- 统计信息 -->
        <div class="stats-grid" id="statsGrid">
            <div class="stat-card">
                <div class="stat-value" id="totalRequests">-</div>
                <div class="stat-label">总请求数</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="successRequests">-</div>
                <div class="stat-label">成功请求</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="errorRequests">-</div>
                <div class="stat-label">失败请求</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="successRate">-</div>
                <div class="stat-label">成功率</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="avgProcessingTime">-</div>
                <div class="stat-label">平均处理时间(ms)</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="redisHitRate">-</div>
                <div class="stat-label">Redis命中率</div>
            </div>
        </div>

        <!-- 筛选器 -->
        <div class="filters">
            <div class="filter-row">
                <div class="filter-group">
                    <label for="statusFilter">状态</label>
                    <select id="statusFilter">
                        <option value="">全部</option>
                        <option value="success">成功</option>
                        <option value="error">失败</option>
                    </select>
                </div>
                <div class="filter-group">
                    <label for="startTime">开始时间</label>
                    <input type="datetime-local" id="startTime">
                </div>
                <div class="filter-group">
                    <label for="endTime">结束时间</label>
                    <input type="datetime-local" id="endTime">
                </div>
                <div class="filter-group">
                    <label for="pageSize">每页显示</label>
                    <select id="pageSize">
                        <option value="10">10条</option>
                        <option value="20" selected>20条</option>
                        <option value="50">50条</option>
                        <option value="100">100条</option>
                    </select>
                </div>
                <div class="filter-group">
                    <label>&nbsp;</label>
                    <button class="btn btn-primary" onclick="searchLogs()">🔍 搜索</button>
                </div>
                <div class="filter-group">
                    <label>&nbsp;</label>
                    <button class="btn btn-secondary" onclick="resetFilters()">🔄 重置</button>
                </div>
            </div>
        </div>

        <!-- 错误提示 -->
        <div id="errorMessage" class="error" style="display: none;"></div>

        <!-- 日志表格 -->
        <div class="table-container">
            <div id="loadingIndicator" class="loading">
                <p>📡 正在加载日志数据...</p>
            </div>
            <table class="table" id="logsTable" style="display: none;">
                <thead>
                    <tr>
                        <th style="width: 60px;">ID</th>
                        <th style="width: 80px;">状态</th>
                        <th style="width: 100px;">处理时间</th>
                        <th style="width: 80px;">Redis命中</th>
                        <th style="width: 80px;">MySQL命中</th>
                        <th style="width: 120px;">用户图片</th>
                        <th style="width: 300px;">响应结果</th>
                        <th style="width: 140px;">创建时间</th>
                        <th style="width: 120px;">操作</th>
                    </tr>
                </thead>
                <tbody id="logsTableBody">
                </tbody>
            </table>
        </div>

        <!-- 分页 -->
        <div class="pagination" id="pagination" style="display: none;">
            <button id="prevPage" onclick="changePage(-1)">« 上一页</button>
            <span id="pageInfo">第 1 页，共 1 页</span>
            <button id="nextPage" onclick="changePage(1)">下一页 »</button>
        </div>
    </div>

    <!-- 日志详情模态框 -->
    <div id="logDetailModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>📋 日志详情</h2>
                <span class="close" onclick="closeModal()">&times;</span>
            </div>
            <div id="logDetailContent">
            </div>
        </div>
    </div>

    <script>
        // API配置
        const API_BASE_URL = 'http://localhost:8080';
        
        // 全局状态
        let currentPage = 1;
        let currentPageSize = 20;
        let totalPages = 1;
        let currentFilters = {};

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadStatistics();
            loadLogs();
        });

        // 加载统计信息
        async function loadStatistics() {
            try {
                const response = await fetch(`${API_BASE_URL}/api/v1/logs/statistics`);
                const result = await response.json();

                if (result.code === 0) {
                    const stats = result.data;
                    document.getElementById('totalRequests').textContent = stats.total_requests.toLocaleString();
                    document.getElementById('successRequests').textContent = stats.success_requests.toLocaleString();
                    document.getElementById('errorRequests').textContent = stats.error_requests.toLocaleString();
                    document.getElementById('successRate').textContent = stats.success_rate.toFixed(1) + '%';
                    document.getElementById('avgProcessingTime').textContent = stats.avg_processing_time.toLocaleString();
                    document.getElementById('redisHitRate').textContent = stats.redis_hit_rate.toFixed(1) + '%';
                }
            } catch (error) {
                console.error('加载统计信息失败:', error);
            }
        }

        // 加载日志列表
        async function loadLogs() {
            showLoading(true);
            hideError();

            try {
                const params = new URLSearchParams({
                    page: currentPage,
                    page_size: currentPageSize,
                    ...currentFilters
                });

                const response = await fetch(`${API_BASE_URL}/api/v1/logs?${params}`);
                const result = await response.json();

                if (result.code === 0) {
                    displayLogs(result.data.list);
                    updatePagination(result.data);
                } else {
                    showError('加载日志失败: ' + result.message);
                }
            } catch (error) {
                showError('网络错误: ' + error.message);
                console.error('加载日志失败:', error);
            } finally {
                showLoading(false);
            }
        }

        // 显示日志列表
        function displayLogs(logs) {
            const tbody = document.getElementById('logsTableBody');
            tbody.innerHTML = '';

            if (logs.length === 0) {
                tbody.innerHTML = '<tr><td colspan="9" style="text-align: center; padding: 40px; color: #7f8c8d;">📭 暂无日志数据</td></tr>';
                return;
            }

            logs.forEach(log => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${log.id}</td>
                    <td><span class="status-badge status-${log.status}">${log.status === 'success' ? '成功' : '失败'}</span></td>
                    <td>${log.processing_time}ms</td>
                    <td>${log.is_redis_hit ? '✅' : '❌'}</td>
                    <td>${log.is_mysql_hit ? '✅' : '❌'}</td>
                    <td>${formatUserImage(log.user_image_url)}</td>
                    <td>${formatResponseData(log.response_data)}</td>
                    <td>${formatDateTime(log.created_at)}</td>
                    <td>
                        <button class="btn btn-primary" onclick="viewLogDetail(${log.id})" style="margin-right: 5px;">查看</button>
                        <button class="btn btn-danger" onclick="deleteLog(${log.id})">删除</button>
                    </td>
                `;
                tbody.appendChild(row);
            });
        }

        // 更新分页信息
        function updatePagination(data) {
            totalPages = data.total_pages;
            document.getElementById('pageInfo').textContent = `第 ${data.page} 页，共 ${data.total_pages} 页 (总计 ${data.total} 条)`;
            document.getElementById('prevPage').disabled = data.page <= 1;
            document.getElementById('nextPage').disabled = data.page >= data.total_pages;
            document.getElementById('pagination').style.display = 'flex';
        }

        // 搜索日志
        function searchLogs() {
            const status = document.getElementById('statusFilter').value;
            const startTime = document.getElementById('startTime').value;
            const endTime = document.getElementById('endTime').value;
            const pageSize = document.getElementById('pageSize').value;

            currentFilters = {};
            if (status) currentFilters.status = status;
            if (startTime) currentFilters.start_time = new Date(startTime).toISOString();
            if (endTime) currentFilters.end_time = new Date(endTime).toISOString();

            currentPage = 1;
            currentPageSize = parseInt(pageSize);
            loadLogs();
        }

        // 重置筛选器
        function resetFilters() {
            document.getElementById('statusFilter').value = '';
            document.getElementById('startTime').value = '';
            document.getElementById('endTime').value = '';
            document.getElementById('pageSize').value = '20';

            currentFilters = {};
            currentPage = 1;
            currentPageSize = 20;
            loadLogs();
        }

        // 翻页
        function changePage(direction) {
            const newPage = currentPage + direction;
            if (newPage >= 1 && newPage <= totalPages) {
                currentPage = newPage;
                loadLogs();
            }
        }

        // 查看日志详情
        async function viewLogDetail(logId) {
            try {
                const response = await fetch(`${API_BASE_URL}/api/v1/logs/${logId}`);
                const result = await response.json();

                if (result.code === 0) {
                    displayLogDetail(result.data);
                    document.getElementById('logDetailModal').style.display = 'block';
                } else {
                    alert('获取日志详情失败: ' + result.message);
                }
            } catch (error) {
                alert('网络错误: ' + error.message);
                console.error('获取日志详情失败:', error);
            }
        }

        // 显示日志详情
        function displayLogDetail(log) {
            const content = document.getElementById('logDetailContent');
            content.innerHTML = `
                <div class="detail-grid">
                    <div class="detail-label">日志ID:</div>
                    <div class="detail-value">${log.id}</div>
                    <div class="detail-label">请求ID:</div>
                    <div class="detail-value">${log.request_id}</div>
                    <div class="detail-label">状态:</div>
                    <div class="detail-value"><span class="status-badge status-${log.status}">${log.status === 'success' ? '成功' : '失败'}</span></div>
                    <div class="detail-label">用户图片URL:</div>
                    <div class="detail-value"><a href="${log.user_image_url}" target="_blank">${log.user_image_url}</a></div>
                    <div class="detail-label">处理时间:</div>
                    <div class="detail-value">${log.processing_time}ms</div>
                    <div class="detail-label">Redis命中:</div>
                    <div class="detail-value">${log.is_redis_hit ? '是' : '否'}</div>
                    <div class="detail-label">MySQL命中:</div>
                    <div class="detail-value">${log.is_mysql_hit ? '是' : '否'}</div>
                    <div class="detail-label">请求IP:</div>
                    <div class="detail-value">${log.request_ip}</div>
                    <div class="detail-label">用户代理:</div>
                    <div class="detail-value">${log.user_agent}</div>
                    <div class="detail-label">创建时间:</div>
                    <div class="detail-value">${formatDateTime(log.created_at)}</div>
                    ${log.error_message ? `
                        <div class="detail-label">错误信息:</div>
                        <div class="detail-value" style="color: #e74c3c;">${log.error_message}</div>
                    ` : ''}
                    ${log.response_data ? `
                        <div class="detail-label">响应数据:</div>
                        <div class="detail-value"><pre style="background: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto;">${JSON.stringify(JSON.parse(log.response_data), null, 2)}</pre></div>
                    ` : ''}
                </div>
            `;
        }

        // 删除日志
        async function deleteLog(logId) {
            if (!confirm('确定要删除这条日志吗？此操作不可恢复。')) {
                return;
            }

            try {
                const response = await fetch(`${API_BASE_URL}/api/v1/logs/${logId}`, {
                    method: 'DELETE'
                });
                const result = await response.json();

                if (result.code === 0) {
                    alert('删除成功');
                    loadLogs(); // 重新加载列表
                    loadStatistics(); // 重新加载统计信息
                } else {
                    alert('删除失败: ' + result.message);
                }
            } catch (error) {
                alert('网络错误: ' + error.message);
                console.error('删除日志失败:', error);
            }
        }

        // 关闭模态框
        function closeModal() {
            document.getElementById('logDetailModal').style.display = 'none';
        }

        // 显示/隐藏加载指示器
        function showLoading(show) {
            document.getElementById('loadingIndicator').style.display = show ? 'block' : 'none';
            document.getElementById('logsTable').style.display = show ? 'none' : 'table';
        }

        // 显示错误信息
        function showError(message) {
            const errorDiv = document.getElementById('errorMessage');
            errorDiv.textContent = message;
            errorDiv.style.display = 'block';
        }

        // 隐藏错误信息
        function hideError() {
            document.getElementById('errorMessage').style.display = 'none';
        }

        // 格式化日期时间
        function formatDateTime(dateString) {
            const date = new Date(dateString);
            return date.toLocaleString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });
        }

        // 格式化用户图片
        function formatUserImage(imageUrl) {
            if (!imageUrl) {
                return '<span style="color: #ccc;">无</span>';
            }

            return `<img src="${imageUrl}" alt="用户图片"
                         style="width: 50px; height: 50px; object-fit: cover; border-radius: 4px; border: 1px solid #ddd; cursor: pointer;"
                         onclick="showImageModal('${imageUrl}')"
                         onerror="this.style.display='none'; this.nextElementSibling.style.display='inline';">
                    <span style="display: none; color: #e74c3c; font-size: 12px;">🖼️ 加载失败</span>`;
        }

        // 格式化响应数据
        function formatResponseData(responseData) {
            if (!responseData) {
                return '<span style="color: #ccc;">无响应数据</span>';
            }

            try {
                const data = JSON.parse(responseData);

                // 如果是数组，取第一个元素
                const item = Array.isArray(data) ? data[0] : data;

                if (!item || typeof item !== 'object') {
                    return '<span style="color: #ccc;">数据格式异常</span>';
                }

                let result = '';

                // 显示题目类型
                if (item.quest_type) {
                    result += `<div style="margin-bottom: 5px;"><strong>类型:</strong> <span style="background: #e3f2fd; color: #1565c0; padding: 2px 6px; border-radius: 10px; font-size: 11px;">${item.quest_type}</span></div>`;
                }

                // 显示题目内容（截断显示）
                if (item.quest_content) {
                    const content = item.quest_content.length > 50
                        ? item.quest_content.substring(0, 50) + '...'
                        : item.quest_content;
                    result += `<div style="margin-bottom: 5px;"><strong>内容:</strong> <span style="color: #555;">${escapeHtml(content)}</span></div>`;
                }

                // 显示答案
                if (item.answer) {
                    result += `<div><strong>答案:</strong> ${formatAnswerData(item.answer)}</div>`;
                }

                return result || '<span style="color: #ccc;">无有效数据</span>';

            } catch (error) {
                return '<span style="color: #e74c3c;">数据解析失败</span>';
            }
        }

        // 格式化答案数据
        function formatAnswerData(answer) {
            if (!answer) return '<span style="color: #ccc;">无</span>';

            if (typeof answer === 'string') {
                return `<span style="color: #27ae60;">${escapeHtml(answer)}</span>`;
            }

            if (typeof answer === 'object') {
                if (Array.isArray(answer)) {
                    // 数组格式
                    return answer.map(item =>
                        `<div style="color: #27ae60; font-size: 12px;">• ${escapeHtml(String(item))}</div>`
                    ).join('');
                } else {
                    // 对象格式
                    return Object.keys(answer).map(key =>
                        `<div style="color: #27ae60; font-size: 12px;"><strong>${key}:</strong> ${escapeHtml(String(answer[key]))}</div>`
                    ).join('');
                }
            }

            return `<span style="color: #27ae60;">${escapeHtml(String(answer))}</span>`;
        }

        // HTML转义函数
        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }

        // 显示图片放大模态框
        function showImageModal(imageUrl) {
            // 创建模态框HTML
            const modalHtml = `
                <div id="imageModal" class="modal" style="display: block;">
                    <div class="modal-content" style="max-width: 90%; max-height: 90%; padding: 0; background: transparent; box-shadow: none;">
                        <div style="position: relative; text-align: center;">
                            <img src="${imageUrl}" alt="用户图片"
                                 style="max-width: 100%; max-height: 80vh; border-radius: 8px; box-shadow: 0 4px 20px rgba(0,0,0,0.3);">
                            <button onclick="closeImageModal()"
                                    style="position: absolute; top: 10px; right: 10px; background: rgba(0,0,0,0.7); color: white; border: none; border-radius: 50%; width: 40px; height: 40px; font-size: 20px; cursor: pointer; display: flex; align-items: center; justify-content: center;">
                                ×
                            </button>
                        </div>
                    </div>
                </div>
            `;

            // 移除已存在的图片模态框
            const existingModal = document.getElementById('imageModal');
            if (existingModal) {
                existingModal.remove();
            }

            // 添加新的模态框
            document.body.insertAdjacentHTML('beforeend', modalHtml);

            // 点击背景关闭
            document.getElementById('imageModal').onclick = function(event) {
                if (event.target === this) {
                    closeImageModal();
                }
            };
        }

        // 关闭图片模态框
        function closeImageModal() {
            const modal = document.getElementById('imageModal');
            if (modal) {
                modal.remove();
            }
        }

        // 格式化用户图片
        function formatUserImage(imageUrl) {
            if (!imageUrl) {
                return '<span style="color: #ccc;">无</span>';
            }

            return `<a href="${imageUrl}" target="_blank" title="点击查看原图" style="text-decoration: none;">
                        <img src="${imageUrl}" alt="用户图片"
                             style="width: 40px; height: 40px; object-fit: cover; border-radius: 4px; border: 1px solid #ddd; cursor: pointer;"
                             onerror="this.style.display='none'; this.nextElementSibling.style.display='inline';">
                        <span style="display: none; color: #e74c3c; font-size: 12px;">🖼️ 加载失败</span>
                    </a>`;
        }

        // 格式化响应数据
        function formatResponseData(responseData) {
            if (!responseData) {
                return '<span style="color: #ccc;">无响应数据</span>';
            }

            try {
                const data = JSON.parse(responseData);

                // 如果是数组，取第一个元素
                const item = Array.isArray(data) ? data[0] : data;

                if (!item || typeof item !== 'object') {
                    return '<span style="color: #ccc;">数据格式异常</span>';
                }

                let result = '';

                // 显示题目类型
                if (item.quest_type) {
                    result += `<div style="margin-bottom: 5px;"><strong>类型:</strong> <span style="background: #e3f2fd; color: #1565c0; padding: 2px 6px; border-radius: 10px; font-size: 11px;">${item.quest_type}</span></div>`;
                }

                // 显示题目内容（截断显示）
                if (item.quest_content) {
                    const content = item.quest_content.length > 50
                        ? item.quest_content.substring(0, 50) + '...'
                        : item.quest_content;
                    result += `<div style="margin-bottom: 5px;"><strong>内容:</strong> <span style="color: #555;">${escapeHtml(content)}</span></div>`;
                }

                // 显示答案
                if (item.answer) {
                    result += `<div><strong>答案:</strong> ${formatAnswerData(item.answer)}</div>`;
                }

                return result || '<span style="color: #ccc;">无有效数据</span>';

            } catch (error) {
                return '<span style="color: #e74c3c;">数据解析失败</span>';
            }
        }

        // 格式化答案数据
        function formatAnswerData(answer) {
            if (!answer) return '<span style="color: #ccc;">无</span>';

            if (typeof answer === 'string') {
                return `<span style="color: #27ae60;">${escapeHtml(answer)}</span>`;
            }

            if (typeof answer === 'object') {
                if (Array.isArray(answer)) {
                    // 数组格式
                    return answer.map(item =>
                        `<div style="color: #27ae60; font-size: 12px;">• ${escapeHtml(String(item))}</div>`
                    ).join('');
                } else {
                    // 对象格式
                    return Object.keys(answer).map(key =>
                        `<div style="color: #27ae60; font-size: 12px;"><strong>${key}:</strong> ${escapeHtml(String(answer[key]))}</div>`
                    ).join('');
                }
            }

            return `<span style="color: #27ae60;">${escapeHtml(String(answer))}</span>`;
        }

        // HTML转义函数
        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }

        // 点击模态框外部关闭
        window.onclick = function(event) {
            const modal = document.getElementById('logDetailModal');
            if (event.target === modal) {
                closeModal();
            }
        }

        // 键盘事件处理
        document.addEventListener('keydown', function(event) {
            if (event.key === 'Escape') {
                closeModal();
                closeImageModal();
            }
        });

        // 自动刷新功能（可选）
        let autoRefreshInterval;

        function toggleAutoRefresh() {
            if (autoRefreshInterval) {
                clearInterval(autoRefreshInterval);
                autoRefreshInterval = null;
                console.log('自动刷新已停止');
            } else {
                autoRefreshInterval = setInterval(() => {
                    loadLogs();
                    loadStatistics();
                }, 30000); // 每30秒刷新一次
                console.log('自动刷新已启动');
            }
        }

        // 导出功能（可选）
        function exportLogs() {
            // 这里可以实现导出CSV或Excel功能
            alert('导出功能开发中...');
        }
    </script>
</body>
</html>
