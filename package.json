{"name": "qwen-solve-admin", "version": "1.0.0", "description": "Qwen-Solve API 管理系统", "main": "server.js", "scripts": {"start": "node server.js", "dev": "node server.js", "build": "pkg . --targets node18-linux-x64 --output dist/qwen-solve-admin-linux"}, "keywords": ["qwen-solve", "admin", "management"], "author": "Qwen-Solve Team", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "compression": "^1.7.4"}, "devDependencies": {"pkg": "^5.8.1"}, "pkg": {"assets": ["public/**/*"], "targets": ["node18-linux-x64"]}}