<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>题库维护管理 - Qwen-Solve API</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f5f5f5;
            color: #333;
            line-height: 1.6;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header h1 {
            color: #2c3e50;
            margin-bottom: 5px;
        }

        .filters {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }

        .filter-row {
            display: flex;
            gap: 15px;
            align-items: end;
            flex-wrap: wrap;
            margin-bottom: 15px;
        }

        .filter-group {
            display: flex;
            flex-direction: column;
            min-width: 150px;
        }

        .filter-group label {
            margin-bottom: 5px;
            font-weight: 500;
            color: #555;
        }

        .filter-group input,
        .filter-group select {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }

        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: background-color 0.3s;
            text-decoration: none;
            display: inline-block;
            text-align: center;
        }

        .btn-primary {
            background-color: #3498db;
            color: white;
        }

        .btn-primary:hover {
            background-color: #2980b9;
        }

        .btn-success {
            background-color: #27ae60;
            color: white;
        }

        .btn-success:hover {
            background-color: #229954;
        }

        .btn-warning {
            background-color: #f39c12;
            color: white;
        }

        .btn-warning:hover {
            background-color: #e67e22;
        }

        .btn-danger {
            background-color: #e74c3c;
            color: white;
        }

        .btn-danger:hover {
            background-color: #c0392b;
        }

        .btn-secondary {
            background-color: #95a5a6;
            color: white;
        }

        .btn-secondary:hover {
            background-color: #7f8c8d;
        }

        .table-container {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
        }

        .table th,
        .table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #eee;
            vertical-align: top;
        }

        .table th {
            background-color: #f8f9fa;
            font-weight: 600;
            color: #555;
            position: sticky;
            top: 0;
            z-index: 10;
        }

        .table tr:hover {
            background-color: #f8f9fa;
        }

        .question-content {
            max-width: 300px;
            max-height: 80px;
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 4;
            -webkit-box-orient: vertical;
            line-height: 1.4;
            font-size: 12px;
        }

        .answer-display {
            max-width: 200px;
            font-size: 12px;
            background-color: #f8f9fa;
            padding: 4px 8px;
            border-radius: 4px;
        }

        .verification-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s;
        }

        .verified {
            background-color: #d4edda;
            color: #155724;
        }

        .unverified {
            background-color: #fff3cd;
            color: #856404;
        }

        .verification-badge:hover {
            opacity: 0.8;
        }

        .type-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }

        .type-single {
            background-color: #e3f2fd;
            color: #1565c0;
        }

        .type-multiple {
            background-color: #f3e5f5;
            color: #7b1fa2;
        }

        .type-judge {
            background-color: #e8f5e8;
            color: #2e7d32;
        }

        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 10px;
            padding: 20px;
            background: white;
            margin-top: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .pagination button {
            padding: 8px 12px;
            border: 1px solid #ddd;
            background: white;
            cursor: pointer;
            border-radius: 4px;
        }

        .pagination button:hover:not(:disabled) {
            background-color: #f8f9fa;
        }

        .pagination button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .pagination .active {
            background-color: #3498db;
            color: white;
            border-color: #3498db;
        }

        .loading {
            text-align: center;
            padding: 40px;
            color: #7f8c8d;
        }

        .error {
            background-color: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
        }

        .success {
            background-color: #d4edda;
            color: #155724;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
        }

        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }

        .modal-content {
            background-color: white;
            margin: 2% auto;
            padding: 0;
            border-radius: 8px;
            width: 90%;
            max-width: 800px;
            max-height: 90vh;
            overflow-y: auto;
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 20px;
            border-bottom: 1px solid #eee;
            background-color: #f8f9fa;
            border-radius: 8px 8px 0 0;
        }

        .modal-body {
            padding: 20px;
        }

        .modal-footer {
            padding: 20px;
            border-top: 1px solid #eee;
            background-color: #f8f9fa;
            border-radius: 0 0 8px 8px;
            text-align: right;
        }

        .close {
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
            color: #aaa;
        }

        .close:hover {
            color: #000;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
            color: #555;
        }

        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 10px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            font-family: inherit;
        }

        .form-group textarea {
            resize: vertical;
            min-height: 80px;
        }

        .options-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }

        .option-group {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .option-group input[type="checkbox"],
        .option-group input[type="radio"] {
            width: auto;
        }

        .image-preview {
            max-width: 200px;
            max-height: 200px;
            border-radius: 4px;
            margin-top: 10px;
        }

        @media (max-width: 768px) {
            .header {
                flex-direction: column;
                align-items: stretch;
                gap: 15px;
            }
            
            .filter-row {
                flex-direction: column;
                align-items: stretch;
            }
            
            .filter-group {
                min-width: auto;
            }
            
            .table-container {
                overflow-x: auto;
            }
            
            .modal-content {
                width: 95%;
                margin: 5% auto;
            }
            
            .options-container {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 页面标题 -->
        <div class="header">
            <div>
                <h1>📚 题库维护管理</h1>
                <p>管理 Qwen-Solve API 的题目数据</p>
            </div>
            <button class="btn btn-success" onclick="openCreateModal()">➕ 新增题目</button>
        </div>

        <!-- 筛选器 -->
        <div class="filters">
            <div class="filter-row">
                <div class="filter-group">
                    <label for="typeFilter">题目类型</label>
                    <select id="typeFilter">
                        <option value="">全部类型</option>
                        <option value="单选题">单选题</option>
                        <option value="多选题">多选题</option>
                        <option value="判断题">判断题</option>
                    </select>
                </div>
                <div class="filter-group">
                    <label for="verifiedFilter">验证状态</label>
                    <select id="verifiedFilter">
                        <option value="">全部状态</option>
                        <option value="true">已验证</option>
                        <option value="false">未验证</option>
                    </select>
                </div>
                <div class="filter-group">
                    <label for="keywordFilter">关键词搜索</label>
                    <input type="text" id="keywordFilter" placeholder="搜索题目内容...">
                </div>
                <div class="filter-group">
                    <label for="pageSize">每页显示</label>
                    <select id="pageSize">
                        <option value="10">10条</option>
                        <option value="20" selected>20条</option>
                        <option value="50">50条</option>
                        <option value="100">100条</option>
                    </select>
                </div>
            </div>
            <div class="filter-row">
                <button class="btn btn-primary" onclick="searchQuestions()">🔍 搜索</button>
                <button class="btn btn-secondary" onclick="resetFilters()">🔄 重置</button>
            </div>
        </div>

        <!-- 消息提示 -->
        <div id="errorMessage" class="error" style="display: none;"></div>
        <div id="successMessage" class="success" style="display: none;"></div>

        <!-- 题目表格 -->
        <div class="table-container">
            <div id="loadingIndicator" class="loading">
                <p>📚 正在加载题目数据...</p>
            </div>
            <table class="table" id="questionsTable" style="display: none;">
                <thead>
                    <tr>
                        <th style="width: 60px;">ID</th>
                        <th style="width: 80px;">类型</th>
                        <th style="width: 300px;">题目内容</th>
                        <th style="width: 200px;">答案</th>
                        <th style="width: 100px;">用户图片</th>
                        <th style="width: 100px;">题干图片</th>
                        <th style="width: 200px;">操作</th>
                    </tr>
                </thead>
                <tbody id="questionsTableBody">
                </tbody>
            </table>
        </div>

        <!-- 分页 -->
        <div class="pagination" id="pagination" style="display: none;">
            <button id="prevPage" onclick="changePage(-1)">« 上一页</button>
            <span id="pageInfo">第 1 页，共 1 页</span>
            <button id="nextPage" onclick="changePage(1)">下一页 »</button>
        </div>
    </div>

    <!-- 题目编辑/创建模态框 -->
    <div id="questionModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 id="modalTitle">📝 新增题目</h2>
                <span class="close" onclick="safeCloseModal()">&times;</span>
            </div>
            <div class="modal-body">
                <form id="questionForm">
                    <div class="form-group">
                        <label for="questType">题目类型 *</label>
                        <select id="questType" required onchange="onTypeChange()">
                            <option value="">请选择题目类型</option>
                            <option value="单选题">单选题</option>
                            <option value="多选题">多选题</option>
                            <option value="判断题">判断题</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="questContent">题目内容 *</label>
                        <textarea id="questContent" required placeholder="请输入题目内容..."></textarea>
                    </div>

                    <div class="form-group">
                        <label for="imageUrl">题目图片URL</label>
                        <input type="url" id="imageUrl" placeholder="https://example.com/image.jpg" onchange="previewImage()">
                        <img id="imagePreview" class="image-preview" style="display: none;">
                    </div>

                    <div class="form-group" id="optionsContainer">
                        <label>选项设置 *</label>
                        <div id="optionsGrid" class="options-container">
                            <!-- 选项将通过JavaScript动态生成 -->
                        </div>
                    </div>

                    <div class="form-group" id="answerContainer">
                        <label>正确答案 *</label>
                        <div id="answerGrid" class="options-container">
                            <!-- 答案选择将通过JavaScript动态生成 -->
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="analysis">答案解析</label>
                        <textarea id="analysis" placeholder="请输入答案解析..."></textarea>
                    </div>

                    <div class="form-group">
                        <label for="hashKey">缓存键值 (Hash Key)</label>
                        <input type="text" id="hashKey" placeholder="请输入缓存键值">
                    </div>

                    <div class="form-group">
                        <label>
                            <input type="checkbox" id="isVerified"> 标记为已验证
                        </label>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="safeCloseModal()">取消</button>
                <button type="button" class="btn btn-primary" onclick="saveQuestion()">保存</button>
            </div>
        </div>
    </div>



    <script>
        // API配置
        const API_BASE_URL = 'http://localhost:8080';
        
        // 全局状态
        let currentPage = 1;
        let currentPageSize = 20;
        let totalPages = 1;
        let currentFilters = {};
        let editingQuestionId = null;

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadQuestions();
        });

        // 加载题目列表
        async function loadQuestions() {
            showLoading(true);
            hideMessages();

            try {
                const params = new URLSearchParams({
                    page: currentPage,
                    page_size: currentPageSize,
                    ...currentFilters
                });

                const response = await fetch(`${API_BASE_URL}/api/v1/questions?${params}`);
                const result = await response.json();

                if (result.code === 0) {
                    displayQuestions(result.data.list);
                    updatePagination(result.data);
                } else {
                    showError('加载题目失败: ' + result.message);
                }
            } catch (error) {
                showError('网络错误: ' + error.message);
                console.error('加载题目失败:', error);
            } finally {
                showLoading(false);
            }
        }

        // 显示题目列表
        function displayQuestions(questions) {
            const tbody = document.getElementById('questionsTableBody');
            tbody.innerHTML = '';

            if (questions.length === 0) {
                tbody.innerHTML = '<tr><td colspan="7" style="text-align: center; padding: 40px; color: #7f8c8d;">📭 暂无题目数据</td></tr>';
                return;
            }

            questions.forEach(question => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${question.id}</td>
                    <td><span class="type-badge type-${getTypeClass(question.quest_type)}">${question.quest_type}</span></td>
                    <td>
                        <div class="question-content" title="${escapeHtml(question.quest_content)}">
                            ${escapeHtml(question.quest_content)}
                        </div>
                    </td>
                    <td>
                        <div class="answer-display">
                            ${formatAnswer(question.answer)}
                        </div>
                    </td>
                    <td>
                        ${formatUserImage(question.user_image)}
                    </td>
                    <td>
                        ${question.image_url ?
                            `<a href="${question.image_url}" target="_blank" title="查看题干图片">🖼️</a>` :
                            '<span style="color: #ccc;">无</span>'
                        }
                    </td>
                    <td>
                        <button class="btn ${question.is_verified ? 'btn-success' : 'btn-danger'}"
                                onclick="toggleVerification(${question.id}, ${!question.is_verified})"
                                style="margin-right: 5px; margin-bottom: 5px;">
                            ${question.is_verified ? '已验证' : '未验证'}
                        </button>
                        <button class="btn btn-warning" onclick="editQuestion(${question.id})" style="margin-right: 5px; margin-bottom: 5px;">编辑</button>
                        <button class="btn btn-danger" onclick="deleteQuestion(${question.id})" style="margin-bottom: 5px;">删除</button>
                    </td>
                `;
                tbody.appendChild(row);
            });
        }

        // 更新分页信息
        function updatePagination(data) {
            totalPages = data.total_pages;
            document.getElementById('pageInfo').textContent = `第 ${data.page} 页，共 ${data.total_pages} 页 (总计 ${data.total} 条)`;
            document.getElementById('prevPage').disabled = data.page <= 1;
            document.getElementById('nextPage').disabled = data.page >= data.total_pages;
            document.getElementById('pagination').style.display = 'flex';
        }

        // 搜索题目
        function searchQuestions() {
            const questType = document.getElementById('typeFilter').value;
            const isVerified = document.getElementById('verifiedFilter').value;
            const keyword = document.getElementById('keywordFilter').value.trim();
            const pageSize = document.getElementById('pageSize').value;

            currentFilters = {};
            if (questType) currentFilters.quest_type = questType;
            if (isVerified !== '') currentFilters.is_verified = isVerified === 'true';
            if (keyword) currentFilters.keyword = keyword;

            currentPage = 1;
            currentPageSize = parseInt(pageSize);
            loadQuestions();
        }

        // 重置筛选器
        function resetFilters() {
            document.getElementById('typeFilter').value = '';
            document.getElementById('verifiedFilter').value = '';
            document.getElementById('keywordFilter').value = '';
            document.getElementById('pageSize').value = '20';

            currentFilters = {};
            currentPage = 1;
            currentPageSize = 20;
            loadQuestions();
        }

        // 翻页
        function changePage(direction) {
            const newPage = currentPage + direction;
            if (newPage >= 1 && newPage <= totalPages) {
                currentPage = newPage;
                loadQuestions();
            }
        }

        // 切换验证状态
        async function toggleVerification(questionId, isVerified) {
            try {
                const response = await fetch(`${API_BASE_URL}/api/v1/questions/${questionId}`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        is_verified: isVerified
                    })
                });
                const result = await response.json();

                if (result.code === 0) {
                    showSuccess(`题目已${isVerified ? '标记为已验证' : '标记为未验证'}`);
                    loadQuestions(); // 重新加载列表以更新按钮状态
                } else {
                    showError('更新验证状态失败: ' + result.message);
                }
            } catch (error) {
                showError('网络错误: ' + error.message);
                console.error('更新验证状态失败:', error);
            }
        }

        // 删除题目
        async function deleteQuestion(questionId) {
            if (!confirm('确定要删除这个题目吗？此操作不可恢复。')) {
                return;
            }

            try {
                const response = await fetch(`${API_BASE_URL}/api/v1/questions/${questionId}`, {
                    method: 'DELETE'
                });
                const result = await response.json();

                if (result.code === 0) {
                    showSuccess('删除成功');
                    loadQuestions(); // 重新加载列表
                } else {
                    showError('删除失败: ' + result.message);
                }
            } catch (error) {
                showError('网络错误: ' + error.message);
                console.error('删除题目失败:', error);
            }
        }





        // 编辑题目
        async function editQuestion(questionId) {
            try {
                const response = await fetch(`${API_BASE_URL}/api/v1/questions/${questionId}`);
                const result = await response.json();

                if (result.code === 0) {
                    editingQuestionId = questionId;
                    fillQuestionForm(result.data);
                    document.getElementById('modalTitle').textContent = '✏️ 编辑题目';
                    document.getElementById('questionModal').style.display = 'block';
                } else {
                    showError('获取题目信息失败: ' + result.message);
                }
            } catch (error) {
                showError('网络错误: ' + error.message);
                console.error('获取题目信息失败:', error);
            }
        }

        // 填充表单数据
        function fillQuestionForm(question) {
            document.getElementById('questType').value = question.quest_type;
            document.getElementById('questContent').value = question.quest_content;
            document.getElementById('imageUrl').value = question.image_url || '';
            document.getElementById('analysis').value = question.analysis || '';
            document.getElementById('hashKey').value = question.hash_key || '';
            document.getElementById('isVerified').checked = question.is_verified;

            // 触发类型变化以生成选项
            onTypeChange();

            // 等待DOM更新后填充数据
            setTimeout(() => {
                // 填充选项
                if (question.quest_options) {
                    Object.keys(question.quest_options).forEach(key => {
                        const input = document.getElementById(`option${key}`);
                        if (input) {
                            input.value = question.quest_options[key];
                        }
                    });
                    // 更新答案选项标签
                    updateAnswerOptions();
                }

                // 填充答案 - 支持多选题的多个答案
                if (question.answer) {
                    // 先清除所有答案选择
                    const questType = question.quest_type;
                    let optionKeys = [];
                    if (questType === '判断题') {
                        optionKeys = ['A', 'B'];
                    } else {
                        optionKeys = ['A', 'B', 'C', 'D'];
                    }

                    optionKeys.forEach(key => {
                        const input = document.getElementById(`answer${key}`);
                        if (input) {
                            input.checked = false;
                        }
                    });

                    // 设置正确答案 - 确保答案内容与选项内容一致
                    Object.keys(question.answer).forEach(key => {
                        const input = document.getElementById(`answer${key}`);
                        const optionInput = document.getElementById(`option${key}`);
                        if (input && optionInput) {
                            // 验证答案内容与选项内容是否一致
                            if (optionInput.value === question.answer[key]) {
                                input.checked = true;
                            }
                        }
                    });
                }

                // 预览图片
                previewImage();
            }, 100);
        }

        // 打开创建模态框
        function openCreateModal() {
            editingQuestionId = null;
            document.getElementById('modalTitle').textContent = '➕ 新增题目';
            document.getElementById('questionForm').reset();
            document.getElementById('optionsGrid').innerHTML = '';
            document.getElementById('answerGrid').innerHTML = '';
            document.getElementById('imagePreview').style.display = 'none';
            document.getElementById('questionModal').style.display = 'block';
        }

        // 题目类型变化处理
        function onTypeChange() {
            const type = document.getElementById('questType').value;
            const optionsGrid = document.getElementById('optionsGrid');
            const answerGrid = document.getElementById('answerGrid');

            optionsGrid.innerHTML = '';
            answerGrid.innerHTML = '';

            if (!type) return;

            let options = [];
            if (type === '判断题') {
                options = [
                    { key: 'A', label: '正确', placeholder: '正确' },
                    { key: 'B', label: '错误', placeholder: '错误' }
                ];
            } else {
                options = [
                    { key: 'A', label: '选项A', placeholder: '请输入选项A的内容' },
                    { key: 'B', label: '选项B', placeholder: '请输入选项B的内容' },
                    { key: 'C', label: '选项C', placeholder: '请输入选项C的内容' },
                    { key: 'D', label: '选项D', placeholder: '请输入选项D的内容' }
                ];
            }

            // 生成选项输入框
            options.forEach(option => {
                const div = document.createElement('div');
                div.innerHTML = `
                    <label for="option${option.key}">${option.key}:</label>
                    <input type="text" id="option${option.key}" placeholder="${option.placeholder}" required
                           onchange="updateAnswerOptions()">
                `;
                optionsGrid.appendChild(div);
            });

            // 生成答案选择
            const inputType = type === '多选题' ? 'checkbox' : 'radio';
            const inputName = type === '多选题' ? 'correctAnswers' : 'correctAnswer';

            options.forEach(option => {
                const div = document.createElement('div');
                div.className = 'option-group';
                div.innerHTML = `
                    <input type="${inputType}" id="answer${option.key}" name="${inputName}" value="${option.key}"
                           onchange="validateAnswerSelection()">
                    <label for="answer${option.key}" id="answerLabel${option.key}">${option.key}</label>
                `;
                answerGrid.appendChild(div);
            });

            // 为判断题设置默认值
            if (type === '判断题') {
                document.getElementById('optionA').value = '正确';
                document.getElementById('optionB').value = '错误';
                updateAnswerOptions();
            }
        }

        // 更新答案选项标签
        function updateAnswerOptions() {
            const type = document.getElementById('questType').value;
            if (!type) return;

            let optionKeys = [];
            if (type === '判断题') {
                optionKeys = ['A', 'B'];
            } else {
                optionKeys = ['A', 'B', 'C', 'D'];
            }

            optionKeys.forEach(key => {
                const optionInput = document.getElementById(`option${key}`);
                const answerLabel = document.getElementById(`answerLabel${key}`);
                if (optionInput && answerLabel) {
                    const content = optionInput.value.trim();
                    answerLabel.textContent = content ? `${key}: ${content}` : key;
                }
            });
        }

        // 验证答案选择
        function validateAnswerSelection() {
            const type = document.getElementById('questType').value;
            if (!type) return;

            const selectedAnswers = [];
            let optionKeys = [];

            if (type === '判断题') {
                optionKeys = ['A', 'B'];
            } else {
                optionKeys = ['A', 'B', 'C', 'D'];
            }

            optionKeys.forEach(key => {
                const answerInput = document.getElementById(`answer${key}`);
                if (answerInput && answerInput.checked) {
                    selectedAnswers.push(key);
                }
            });

            // 实时验证提示
            const errorDiv = document.getElementById('answerError');
            if (errorDiv) {
                errorDiv.remove();
            }

            let errorMessage = '';
            if (type === '单选题' || type === '判断题') {
                if (selectedAnswers.length > 1) {
                    errorMessage = `${type}只能选择一个答案`;
                }
            } else if (type === '多选题') {
                if (selectedAnswers.length >= 4) {
                    errorMessage = '多选题不能选择全部选项';
                }
            }

            if (errorMessage) {
                const answerContainer = document.getElementById('answerContainer');
                const errorDiv = document.createElement('div');
                errorDiv.id = 'answerError';
                errorDiv.style.color = '#e74c3c';
                errorDiv.style.fontSize = '12px';
                errorDiv.style.marginTop = '5px';
                errorDiv.textContent = errorMessage;
                answerContainer.appendChild(errorDiv);
            }
        }

        // 验证题目类型
        function validateQuestionType(questType) {
            const validTypes = ['单选题', '多选题', '判断题'];
            return validTypes.includes(questType);
        }

        // 验证选项格式
        function validateQuestionOptions(questType, options) {
            if (!options || typeof options !== 'object') {
                return { valid: false, message: '选项必须是对象格式' };
            }

            const keys = Object.keys(options);

            if (questType === '判断题') {
                // 判断题验证
                if (keys.length !== 2) {
                    return { valid: false, message: '判断题必须有且仅有2个选项' };
                }
                const validKeysYN = ['Y', 'N'];
                const validKeysAB = ['A', 'B'];
                const isValidYN = validKeysYN.every(key => keys.includes(key));
                const isValidAB = validKeysAB.every(key => keys.includes(key));

                if (!isValidYN && !isValidAB) {
                    return { valid: false, message: '判断题选项键名必须是Y、N或A、B' };
                }
            } else {
                // 单选题/多选题验证
                if (keys.length !== 4) {
                    return { valid: false, message: '单选题/多选题必须有且仅有4个选项' };
                }
                const requiredKeys = ['A', 'B', 'C', 'D'];
                if (!requiredKeys.every(key => keys.includes(key))) {
                    return { valid: false, message: '单选题/多选题选项键名必须是A、B、C、D' };
                }
            }

            // 验证选项内容不为空
            for (const [key, value] of Object.entries(options)) {
                if (!value || typeof value !== 'string' || value.trim() === '') {
                    return { valid: false, message: `选项${key}的内容不能为空` };
                }
            }

            return { valid: true };
        }

        // 验证答案格式
        function validateAnswer(questType, options, answer) {
            if (!answer || typeof answer !== 'object') {
                return { valid: false, message: '答案必须是对象格式' };
            }

            const answerKeys = Object.keys(answer);
            const optionKeys = Object.keys(options);

            // 验证答案键是否存在于选项中
            for (const key of answerKeys) {
                if (!optionKeys.includes(key)) {
                    return { valid: false, message: `答案选项${key}在题目选项中不存在` };
                }
            }

            // 验证答案内容与选项内容是否一致
            for (const [key, value] of Object.entries(answer)) {
                if (options[key] !== value) {
                    return { valid: false, message: `答案选项${key}的内容与题目选项不一致` };
                }
            }

            // 验证答案数量
            if (questType === '单选题' || questType === '判断题') {
                if (answerKeys.length !== 1) {
                    return { valid: false, message: `${questType}必须有且仅有1个答案` };
                }
            } else if (questType === '多选题') {
                if (answerKeys.length < 1 || answerKeys.length >= 4) {
                    return { valid: false, message: '多选题答案数量必须在1-3个之间' };
                }
            }

            return { valid: true };
        }

        // 完整表单验证
        function validateQuestionForm(formData) {
            const errors = [];

            // 验证必填字段
            if (!formData.quest_type) {
                errors.push('题目类型不能为空');
            } else if (!validateQuestionType(formData.quest_type)) {
                errors.push('题目类型无效');
            }

            if (!formData.quest_content || formData.quest_content.trim() === '') {
                errors.push('题目内容不能为空');
            } else if (formData.quest_content.length > 65535) {
                errors.push('题目内容不能超过65535个字符');
            }

            if (!formData.analysis || formData.analysis.trim() === '') {
                errors.push('答案解析不能为空');
            } else if (formData.analysis.length > 65535) {
                errors.push('答案解析不能超过65535个字符');
            }

            // 验证选项
            const optionsValidation = validateQuestionOptions(formData.quest_type, formData.quest_options);
            if (!optionsValidation.valid) {
                errors.push(optionsValidation.message);
            }

            // 验证答案
            if (optionsValidation.valid) {
                const answerValidation = validateAnswer(formData.quest_type, formData.quest_options, formData.answer);
                if (!answerValidation.valid) {
                    errors.push(answerValidation.message);
                }
            }

            // 验证图片URL（如果提供）
            if (formData.image_url && formData.image_url.trim() !== '') {
                if (formData.image_url.length > 500) {
                    errors.push('图片URL不能超过500个字符');
                } else {
                    const urlPattern = /^https?:\/\/.+/;
                    if (!urlPattern.test(formData.image_url)) {
                        errors.push('图片URL格式无效，必须以http://或https://开头');
                    }
                }
            }

            return {
                valid: errors.length === 0,
                errors: errors
            };
        }

        // 保存题目
        async function saveQuestion() {
            const form = document.getElementById('questionForm');
            if (!form.checkValidity()) {
                form.reportValidity();
                return;
            }

            const questType = document.getElementById('questType').value;
            const questContent = document.getElementById('questContent').value.trim();
            const imageUrl = document.getElementById('imageUrl').value.trim();
            const analysis = document.getElementById('analysis').value.trim();
            const hashKey = document.getElementById('hashKey').value.trim();
            const isVerified = document.getElementById('isVerified').checked;

            // 收集选项
            const questOptions = {};
            const type = questType;
            let optionKeys = [];

            if (type === '判断题') {
                optionKeys = ['A', 'B'];
            } else {
                optionKeys = ['A', 'B', 'C', 'D'];
            }

            // 收集所有选项内容
            for (const key of optionKeys) {
                const input = document.getElementById(`option${key}`);
                if (input && input.value.trim()) {
                    questOptions[key] = input.value.trim();
                } else {
                    showError(`选项${key}不能为空`);
                    return;
                }
            }

            // 收集答案 - 确保答案内容与选项内容完全一致
            const answer = {};
            const selectedAnswers = [];

            for (const key of optionKeys) {
                const input = document.getElementById(`answer${key}`);
                if (input && input.checked) {
                    // 答案的值必须与选项的值完全一致
                    answer[key] = questOptions[key];
                    selectedAnswers.push(key);
                }
            }

            // 验证答案选择
            if (selectedAnswers.length === 0) {
                showError('请至少选择一个正确答案');
                return;
            }

            // 根据题型验证答案数量
            if (type === '单选题' || type === '判断题') {
                if (selectedAnswers.length > 1) {
                    showError(`${type}只能选择一个正确答案`);
                    return;
                }
            } else if (type === '多选题') {
                if (selectedAnswers.length >= 4) {
                    showError('多选题不能选择全部选项作为答案');
                    return;
                }
                if (selectedAnswers.length < 1) {
                    showError('多选题至少需要选择一个正确答案');
                    return;
                }
            }

            // 构建表单数据进行验证
            const formData = {
                quest_type: questType,
                quest_content: questContent,
                quest_options: questOptions,
                answer: answer,
                analysis: analysis,
                image_url: imageUrl
            };

            // 执行完整验证
            const validation = validateQuestionForm(formData);
            if (!validation.valid) {
                showError('表单验证失败：\n' + validation.errors.join('\n'));
                return;
            }

            const questionData = {
                quest_type: questType,
                quest_content: questContent,
                quest_options: questOptions,
                answer: answer,
                analysis: analysis,
                is_verified: isVerified,
                hash_key: hashKey  // 直接保存管理员输入的值，无论是否为空
            };

            if (imageUrl) {
                questionData.image_url = imageUrl;
            }

            try {
                let response;
                if (editingQuestionId) {
                    // 更新题目
                    response = await fetch(`${API_BASE_URL}/api/v1/questions/${editingQuestionId}`, {
                        method: 'PUT',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify(questionData)
                    });
                } else {
                    // 创建题目
                    response = await fetch(`${API_BASE_URL}/api/v1/questions`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify(questionData)
                    });
                }

                const result = await response.json();

                if (result.code === 0) {
                    showSuccess(editingQuestionId ? '题目更新成功' : '题目创建成功');
                    closeModal();
                    loadQuestions(); // 重新加载列表
                } else {
                    showError((editingQuestionId ? '更新' : '创建') + '失败: ' + result.message);
                }
            } catch (error) {
                showError('网络错误: ' + error.message);
                console.error('保存题目失败:', error);
            }
        }

        // 图片预览
        function previewImage() {
            const url = document.getElementById('imageUrl').value.trim();
            const preview = document.getElementById('imagePreview');

            if (url) {
                preview.src = url;
                preview.style.display = 'block';
                preview.onerror = function() {
                    this.style.display = 'none';
                    showError('图片加载失败，请检查URL是否正确');
                };
            } else {
                preview.style.display = 'none';
            }
        }



        // 关闭模态框
        function closeModal() {
            document.getElementById('questionModal').style.display = 'none';
        }

        // 安全关闭模态框（带确认）
        function safeCloseModal() {
            if (hasFormContent()) {
                if (confirm('您有未保存的内容，确定要关闭吗？')) {
                    closeModal();
                }
            } else {
                closeModal();
            }
        }



        // 显示/隐藏加载指示器
        function showLoading(show) {
            document.getElementById('loadingIndicator').style.display = show ? 'block' : 'none';
            document.getElementById('questionsTable').style.display = show ? 'none' : 'table';
        }

        // 显示错误信息
        function showError(message) {
            hideMessages();
            const errorDiv = document.getElementById('errorMessage');
            errorDiv.textContent = message;
            errorDiv.style.display = 'block';
            setTimeout(() => {
                errorDiv.style.display = 'none';
            }, 5000);
        }

        // 显示成功信息
        function showSuccess(message) {
            hideMessages();
            const successDiv = document.getElementById('successMessage');
            successDiv.textContent = message;
            successDiv.style.display = 'block';
            setTimeout(() => {
                successDiv.style.display = 'none';
            }, 3000);
        }

        // 隐藏所有消息
        function hideMessages() {
            document.getElementById('errorMessage').style.display = 'none';
            document.getElementById('successMessage').style.display = 'none';
        }

        // 工具函数
        function getTypeClass(questType) {
            switch (questType) {
                case '单选题': return 'single';
                case '多选题': return 'multiple';
                case '判断题': return 'judge';
                default: return 'single';
            }
        }

        function formatAnswer(answer) {
            if (!answer || typeof answer !== 'object') return '无';
            return Object.keys(answer).map(key => `${key}: ${answer[key]}`).join('<br>');
        }

        function formatOptionsForDisplay(options) {
            if (!options || typeof options !== 'object') return '无';
            return Object.keys(options).map(key => `<div><strong>${key}:</strong> ${escapeHtml(options[key])}</div>`).join('');
        }

        function formatDateTime(dateString) {
            const date = new Date(dateString);
            return date.toLocaleString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit'
            });
        }

        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }

        // 格式化用户图片
        function formatUserImage(imageUrl) {
            if (!imageUrl) {
                return '<span style="color: #ccc;">无</span>';
            }

            return `<img src="${imageUrl}" alt="用户图片"
                         style="width: 40px; height: 40px; object-fit: cover; border-radius: 4px; border: 1px solid #ddd; cursor: pointer;"
                         onclick="showImageModal('${imageUrl}')"
                         onerror="this.style.display='none'; this.nextElementSibling.style.display='inline';">
                    <span style="display: none; color: #e74c3c; font-size: 12px;">🖼️ 加载失败</span>`;
        }

        // 显示图片放大模态框
        function showImageModal(imageUrl) {
            // 创建模态框HTML
            const modalHtml = `
                <div id="imageModal" class="modal" style="display: block; z-index: 2000;">
                    <div class="modal-content" style="max-width: 80%; max-height: 80%; padding: 20px; background: white; box-shadow: 0 8px 32px rgba(0,0,0,0.3); border-radius: 12px; position: relative;">
                        <div style="text-align: center; position: relative;">
                            <button onclick="closeImageModal()"
                                    style="position: absolute; top: -10px; right: -10px; background: #e74c3c; color: white; border: none; border-radius: 50%; width: 30px; height: 30px; font-size: 16px; cursor: pointer; display: flex; align-items: center; justify-content: center; z-index: 2001;">
                                ×
                            </button>
                            <img src="${imageUrl}" alt="用户图片"
                                 style="max-width: 100%; max-height: 70vh; border-radius: 8px; box-shadow: 0 2px 12px rgba(0,0,0,0.2);">
                            <div style="margin-top: 15px; color: #666; font-size: 14px;">
                                点击空白处或按ESC键关闭
                            </div>
                        </div>
                    </div>
                </div>
            `;

            // 移除已存在的图片模态框
            const existingModal = document.getElementById('imageModal');
            if (existingModal) {
                existingModal.remove();
            }

            // 添加新的模态框
            document.body.insertAdjacentHTML('beforeend', modalHtml);

            // 点击背景关闭
            const modal = document.getElementById('imageModal');
            modal.onclick = function(event) {
                if (event.target === this) {
                    closeImageModal();
                }
            };

            // 阻止模态框内容区域的点击事件冒泡
            modal.querySelector('.modal-content').onclick = function(event) {
                event.stopPropagation();
            };
        }

        // 关闭图片模态框
        function closeImageModal() {
            const modal = document.getElementById('imageModal');
            if (modal) {
                modal.remove();
            }
        }

        // 事件处理
        window.onclick = function(event) {
            // 题目编辑/新增模态框不允许点击空白处关闭，避免误操作丢失数据
            // 只能通过取消按钮或ESC键关闭
        }

        document.addEventListener('keydown', function(event) {
            if (event.key === 'Escape') {
                // 检查题目编辑模态框是否打开
                const questionModal = document.getElementById('questionModal');
                if (questionModal && questionModal.style.display === 'block') {
                    // 检查是否有输入内容
                    if (hasFormContent()) {
                        if (confirm('您有未保存的内容，确定要关闭吗？')) {
                            closeModal();
                        }
                    } else {
                        closeModal();
                    }
                } else {
                    // 关闭图片模态框
                    closeImageModal();
                }
            }
        });

        // 检查表单是否有内容
        function hasFormContent() {
            const questType = document.getElementById('questType').value;
            const questContent = document.getElementById('questContent').value.trim();
            const imageUrl = document.getElementById('imageUrl').value.trim();
            const analysis = document.getElementById('analysis').value.trim();

            // 检查基本字段（不包括hash_key，因为它只是一个可选的标识符）
            if (questType || questContent || imageUrl || analysis) {
                return true;
            }

            // 检查选项是否有内容
            const optionKeys = ['A', 'B', 'C', 'D'];
            for (const key of optionKeys) {
                const input = document.getElementById(`option${key}`);
                if (input && input.value.trim()) {
                    return true;
                }
            }

            // 检查是否有选择答案
            for (const key of optionKeys) {
                const input = document.getElementById(`answer${key}`);
                if (input && input.checked) {
                    return true;
                }
            }

            return false;
        }

        // 搜索框回车事件
        document.getElementById('keywordFilter').addEventListener('keypress', function(event) {
            if (event.key === 'Enter') {
                searchQuestions();
            }
        });
    </script>
</body>
</html>
